# Generated by Clash Verge

mode: rule
mixed-port: 7897
allow-lan: true
log-level: warning
ipv6: true
external-controller: 127.0.0.1:4570
secret: '144211'
unified-delay: true
maintenance:
  enable-auto-gc: true
  gc-interval: 7200
  enable-connection-cleanup: true
  cleanup-interval: 3600
  enable-memory-monitor: true
  memory-check-interval: 1800
  enable-performance-log: true
  enable-health-check: true
  health-check-interval: 7200
tun:
  auto-detect-interface: true
  auto-route: true
  device: Mihomo
  dns-hijack:
  - any:53
  mtu: 1500
  stack: mixed
  strict-route: true
  enable: true
connection-pool-size: 128
geodata-mode: true
log-max-age: 7
log-max-size: 50
aggressive-gc: false
socket-mark: 0
tcp-fast-open: true
find-process-mode: always
bind-address: '*'
keep-alive-interval: 300
tcp-no-delay: true
profile-cache-size: 2048
rule-cache-size: 1024
cache-cleanup-interval: 900
memory-limit: 8192
geox-url:
  geoip: https://fastgh.lainbo.com/https://github.com/MetaCubeX/meta-rules-dat/releases/download/latest/geoip-lite.dat
  geosite: https://fastgh.lainbo.com/https://github.com/MetaCubeX/meta-rules-dat/releases/download/latest/geosite.dat
  mmdb: https://fastgh.lainbo.com/https://github.com/MetaCubeX/meta-rules-dat/releases/download/latest/country-lite.mmdb
  asn: https://fastgh.lainbo.com/https://github.com/xishang0128/geoip/releases/download/latest/GeoLite2-ASN.mmdb
write-buffer-size: 131072
geodata-loader: standard
read-buffer-size: 131072
proxy-cache-size: 64
log-file: ./logs/clash.log
geo-update-interval: 24
connection-idle-timeout: 600
profile:
  store-selected: true
  store-fake-ip: true
enable-performance-monitor: true
runtime-monitor:
  enable: true
  memory-warning-threshold: 85
  memory-critical-threshold: 95
  connection-warning-threshold: 20000
  uptime-check-interval: 7200
  auto-gc-interval: 7200
  log-performance-stats: true
geo-auto-update: true
dns-cache-size: 256
max-concurrent-streams: 100
dns:
  enable: true
  listen: 0.0.0.0:1053
  ipv6: true
  prefer-h3: false
  respect-rules: true
  use-system-hosts: true
  cache-algorithm: arc
  concurrent: 2
  fallback-delay: 200
  cache-ttl: 1200
  enhanced-mode: fake-ip
  fake-ip-range: **********/16
  fake-ip-cache-size: 32768
  fake-ip-filter:
  - +.lan
  - +.local
  - +.msftconnecttest.com
  - +.msftncsi.com
  - localhost.ptlogin2.qq.com
  - localhost.sec.qq.com
  - localhost.work.weixin.qq.com
  - +.cursor.sh
  - +.cursor.com
  - +.api.cursor.sh
  - +.api.cursor.com
  - +.auth.cursor.sh
  - +.auth.cursor.com
  - +.marketplace.cursorapi.com
  - +.cursor-cdn.com
  - +.elevenlabs.io
  - +.download.todesktop.com
  - +.openai.com
  - +.api.openai.com
  - +.claude.ai
  - +.api.claude.ai
  - +.gemini.google.com
  - +.srv.nintendo.net
  - +.stun.playstation.net
  - +.xboxlive.com
  - xbox.*.microsoft.com
  - '*.battlenet.com.cn'
  - '*.battlenet.com'
  - '*.blzstatic.cn'
  - '*.battle.net'
  default-nameserver:
  - *******
  - *******
  - ************
  - *********
  - *********
  - ************
  nameserver:
  - https://doh.pub/dns-query
  - https://dns.alidns.com/dns-query
  - https://*******/dns-query
  - https://*******/dns-query#ecs=***************/24&ecs-override=true
  - https://cloudflare-dns.com/dns-query
  - https://*********/dns-query
  - https://**************/dns-query#ecs=*******/24&ecs-override=true
  - https://*******/dns-query
  proxy-server-nameserver:
  - https://*******/dns-query
  - https://*******/dns-query
  - https://doh.pub/dns-query
  - https://dns.alidns.com/dns-query
  - https://*******/dns-query
  - https://**************/dns-query
  direct-nameserver:
  - system
  - https://doh.pub/dns-query
  - https://dns.alidns.com/dns-query
  - 114.114.114.114
  - 114.114.115.115
  - 101.226.4.6
  - 123.125.81.6
  - ************
  - 112.124.47.27
  - 114.215.126.16
  direct-nameserver-follow-policy: false
  nameserver-policy:
    geosite:cn:
    - https://*********/dns-query
    - https://doh.pub/dns-query
enable-statistics: true
memory-optimization:
  enable-memory-pool: true
  memory-pool-size: 2048
  enable-zero-copy: true
  enable-splice: true
  prefetch-dns: true
  prefetch-connections: 32
  aggressive-gc: false
  memory-pressure-threshold: 90
  adaptive-buffer-size: true
  smart-memory-management: true
  auto-cleanup-interval: 3600
  connection-reuse-limit: 500
enable-memory-monitor: true
global-client-fingerprint: chrome
tcp-concurrent: true
statistics-interval: 600
http2-settings:
  header-table-size: 65536
  enable-push: false
  max-concurrent-streams: 100
  initial-window-size: 1048576
  max-frame-size: 32768
  ping-timeout: 30
  ping-interval: 60
stability-enhancements:
  enable-connection-reuse-limit: true
  connection-reuse-max: 100
  enable-memory-pressure-detection: true
  memory-pressure-action: cleanup
  enable-automatic-restart: false
  connection-leak-detection: true
  dns-cache-auto-cleanup: true
  rule-cache-validation: true
  proxy-health-auto-recovery: true
memory-pressure-threshold: 85
external-controller-cors:
  allow-private-network: true
  allow-origins:
  - tauri://localhost
  - http://tauri.localhost
  - https://yacd.metacubex.one
  - https://metacubex.github.io
  - https://board.zash.run.place
geoip-cache-size: 512
tcp-keep-alive: true
connection-cache-size: 512
experimental:
  ignore-resolve-fail: true
  sniff-tls-sni: true
  fingerprint: chrome
  quic-go-disable-gso: false
  quic-go-disable-ecn: false
  dialer-ip-version: dual
  tcp-concurrent: true
  interface-name: ''
  routing-mark: 0
sniffer:
  enable: true
  parse-pure-ip: true
  sniff:
    TLS:
      ports:
      - 443
      - 8443
    HTTP:
      ports:
      - 80
      - 8080-8880
      override-destination: true
    QUIC:
      ports:
      - 443
      - 8443
  skip-domain:
  - Mijia Cloud
  - +.apple.com
  - +.icloud.com
max-open-files: 65536
enable-connection-monitor: true
log-max-backups: 3
long-term-optimization:
  auto-restart:
    enable: false
    memory-threshold: 95
    uptime-threshold: 604800
    connection-threshold: 50000
  log-rotation:
    enable: true
    max-size: 50MB
    max-files: 3
    compress: true
  health-check:
    enable: true
    interval: 3600
    memory-check: true
    connection-check: true
    dns-check: true
  cleanup:
    enable: true
    interval: 7200
    clear-dns-cache: false
    clear-connection-cache: false
    clear-rule-cache: false
    gc-force: false
error-recovery:
  enable: true
  max-retry-attempts: 3
  retry-delay: 5000
  circuit-breaker-threshold: 5
  circuit-breaker-timeout: 30000
  auto-fallback: true
  fallback-proxy: DIRECT
  health-check-on-failure: true
adaptive-memory: true
proxies:
- name: US-DigitalOcean,_LLC
  type: vmess
  server: www.visa.com.tw
  port: 443
  cipher: none
  uuid: 8d4b9782-538f-47c3-a725-11d1655f8da5
  tls: true
  alterId: 0
  servername: numerical-diary-directory-brunei.trycloudflare.com
  network: ws
  ws-opts:
    path: /vmess-argo?ed=2048
    headers:
      Host: numerical-diary-directory-brunei.trycloudflare.com
- name: 🇳🇱 hysteria2-nl
  server: ***************
  port: 33666
  type: hysteria2
  password: a3934948-a765-4f70-8605-8d97e57fb12e
  auth: a3934948-a765-4f70-8605-8d97e57fb12e
  skip-cert-verify: true
  udp: true
- name: 🇩🇪 hysteria2-de
  server: *************
  port: 33366
  type: hysteria2
  password: 5b39a426-7d49-4b7e-80db-7215d1b50b8c
  auth: 5b39a426-7d49-4b7e-80db-7215d1b50b8c
  skip-cert-verify: true
  udp: true
- name: 🇺🇸 hysteria2-us
  server: **************
  port: 17888
  type: hysteria2
  password: f92d4721-67d7-4fb1-9291-6a968384dd36
  auth: f92d4721-67d7-4fb1-9291-6a968384dd36
  skip-cert-verify: true
  udp: true
- name: 🎯 Webshare-US专线-Direct
  type: socks5
  server: **************
  port: 5067
  username: bmfofebu
  password: 1v5sfkbfsoml
  udp: true
  dialer-proxy: 🔗 直连
  skip-cert-verify: true
  tfo: true
  hidden: false
- name: 🇺🇸 VLESS-Reality-Direct
  type: vless
  server: ***********
  port: 37571
  uuid: 8d4b9782-538f-47c3-a725-11d1655f8da5
  flow: xtls-rprx-vision
  network: tcp
  tls: true
  reality-opts:
    public-key: OXHOn9V-I0RNYm_jYescBGgjaDfHvgMAsFNOwM2-ZHk
    short-id: ''
  servername: www.iij.ad.jp
  client-fingerprint: chrome
  udp: true
  dialer-proxy: 🔗 直连
  hidden: true
- name: 🇺🇸 Hysteria2-Direct
  type: hysteria2
  server: ***********
  port: 37574
  password: 8d4b9782-538f-47c3-a725-11d1655f8da5
  sni: www.bing.com
  skip-cert-verify: true
  alpn:
  - h3
  udp: true
  dialer-proxy: 🔗 直连
  hidden: true
- name: 🇺🇸 TUIC-Direct
  type: tuic
  server: ***********
  port: 37573
  uuid: 8d4b9782-538f-47c3-a725-11d1655f8da5
  password: kpuSGR6oypGgYrSszISd10et
  sni: www.bing.com
  skip-cert-verify: true
  alpn:
  - h3
  congestion-controller: bbr
  udp-relay-mode: native
  udp: true
  dialer-proxy: 🔗 直连
  hidden: true
- name: 🇺🇸 VLESS-Reality-HK-Relay
  type: vless
  server: ***********
  port: 37571
  uuid: 8d4b9782-538f-47c3-a725-11d1655f8da5
  flow: xtls-rprx-vision
  network: tcp
  tls: true
  reality-opts:
    public-key: OXHOn9V-I0RNYm_jYescBGgjaDfHvgMAsFNOwM2-ZHk
    short-id: ''
  servername: www.iij.ad.jp
  client-fingerprint: chrome
  udp: true
  dialer-proxy: ⚡ 深港专线
  hidden: true
- name: 🔗 Landing-HK-Relay
  type: socks5
  server: **************
  port: 5067
  username: bmfofebu
  password: 1v5sfkbfsoml
  udp: true
  dialer-proxy: ⚡ 深港专线
  hidden: true
- name: 🚀 Landing-Auto-Relay
  type: socks5
  server: **************
  port: 5067
  username: bmfofebu
  password: 1v5sfkbfsoml
  udp: true
  dialer-proxy: ⚡ 深港专线
  hidden: true
- name: 🚄 Landing-Fast-Relay
  type: socks5
  server: **************
  port: 5067
  username: bmfofebu
  password: 1v5sfkbfsoml
  udp: true
  dialer-proxy: 🚄 3.0倍率
  hidden: true
- name: 🇺🇸 Landing-US-Relay
  type: socks5
  server: **************
  port: 5067
  username: bmfofebu
  password: 1v5sfkbfsoml
  udp: true
  dialer-proxy: 🇺🇸 美国
  hidden: true
- name: 🏠 Landing-Home-Relay
  type: socks5
  server: **************
  port: 5067
  username: bmfofebu
  password: 1v5sfkbfsoml
  udp: true
  dialer-proxy: 🏠 家宽
  hidden: true
proxy-providers:
  cordcloud:
    type: http
    url: https://www.ccsub.org/link/f9ZZc2KlgR8rcK3T?clash=1
    interval: 43200
    timeout: 60000
    proxy: DIRECT
    override:
      additional-prefix: 'CC | '
      udp: true
      skip-cert-verify: false
      tcp-no-delay: true
      tcp-keep-alive: true
    health-check:
      enable: false
  creativity-1:
    type: http
    url: https://cady-cdnkckjkfa.cn-shenzhen.fcapp.run/api/v1/client/subscribe?token=db29f7b180ccf72e1ce3957e104a5797
    interval: 43200
    timeout: 60000
    proxy: DIRECT
    override:
      additional-prefix: 'CT1 | '
      udp: true
      skip-cert-verify: false
      tcp-no-delay: true
      tcp-keep-alive: true
    health-check:
      enable: false
  宝可梦-1:
    type: http
    url: https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7
    interval: 21600
    timeout: 60000
    proxy: DIRECT
    override:
      additional-prefix: 'PKM1 | '
      udp: true
      skip-cert-verify: false
      tcp-no-delay: true
      tcp-keep-alive: true
    health-check:
      enable: false
  免流云:
    type: http
    url: https://mly1.543412546.xyz/api/v1/client/subscribe?token=6892b976af2bc84e12c2729d17a01372
    interval: 21600
    timeout: 60000
    proxy: DIRECT
    override:
      additional-prefix: 'MLY | '
      udp: true
      skip-cert-verify: false
      tcp-no-delay: true
      tcp-keep-alive: true
    health-check:
      enable: false
proxy-groups:
- type: select
  disable-udp: false
  hidden: false
  include-all: false
  interval: 0
  timeout: 0
  lazy: true
  max-failed-times: 0
  name: 🔰 模式选择
  proxies:
  - ⚙️ 自动选择
  - 🔗 直连
- interval: 900
  timeout: 8000
  url: http://www.gstatic.com/generate_204
  lazy: true
  max-failed-times: 5
  hidden: false
  disable-udp: false
  filter: (?i)(2.5|1.7)
  exclude-filter: ((?i)(free|免费))|((?i)(landing|落地|webshare))
  tcp-keep-alive: true
  tcp-no-delay: true
  tcp-fast-open: true
  connection-reuse: true
  persistent-connection: false
  connection-idle-timeout: 300
  expected-status: 204
  tolerance: 100
  type: url-test
  strategy: sticky-sessions
  include-all: false
  use:
  - cordcloud
  - creativity-1
  - 宝可梦-1
  - 免流云
  name: ⚡ 深港专线
  icon: https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/Magic.png
- interval: 600
  timeout: 5000
  url: http://www.gstatic.com/generate_204
  lazy: true
  max-failed-times: 3
  hidden: false
  disable-udp: false
  filter: (?i)([3-9]x|[3-9]倍|倍率:[3-9]|倍率:3\d*|高倍|超高|极速|三倍|四倍|五倍)
  exclude-filter: ((?i)(free|免费))|((?i)(landing|落地|webshare))
  tcp-keep-alive: true
  tcp-no-delay: true
  tcp-fast-open: true
  connection-reuse: true
  persistent-connection: false
  connection-idle-timeout: 300
  expected-status: 204
  tolerance: 50
  type: url-test
  strategy: sticky-sessions
  include-all: false
  use:
  - cordcloud
  - creativity-1
  - 宝可梦-1
  - 免流云
  name: 🚄 3.0倍率
  icon: https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/Speedtest.png
- interval: 600
  timeout: 5000
  url: http://www.gstatic.com/generate_204
  lazy: true
  max-failed-times: 3
  hidden: false
  disable-udp: false
  filter: 美国|US|🇺🇸
  exclude-filter: ((?i)(free|免费|ai|专用))|((?i)(landing|落地|webshare))
  tcp-keep-alive: true
  tcp-no-delay: true
  tcp-fast-open: true
  connection-reuse: true
  persistent-connection: false
  connection-idle-timeout: 300
  expected-status: 204
  tolerance: 100
  type: url-test
  strategy: sticky-sessions
  include-all: false
  use:
  - cordcloud
  - creativity-1
  - 宝可梦-1
  - 免流云
  name: 🇺🇸 美国
  icon: https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/United_States.png
- type: select
  disable-udp: false
  hidden: false
  include-all: false
  interval: 0
  timeout: 0
  lazy: true
  max-failed-times: 0
  strategy: sticky-sessions
  use:
  - cordcloud
  - creativity-1
  - 宝可梦-1
  - 免流云
  name: 🇺🇸 美国-AI
  filter: 美国|US|🇺🇸
  icon: https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/United_States.png
  exclude-filter: (?i)(free|免费|游戏|game|netflix|流媒体)
- type: select
  disable-udp: false
  hidden: false
  include-all: false
  interval: 0
  timeout: 0
  lazy: true
  max-failed-times: 0
  name: 🎯 落地节点
  proxies:
  - 🎯 Webshare-US专线-Direct
  - 🇺🇸 VLESS-Reality-Direct
  - 🇺🇸 Hysteria2-Direct
  - 🇺🇸 TUIC-Direct
  - 🇺🇸 VLESS-Reality-HK-Relay
  - 🔗 Landing-HK-Relay
  - 🚀 Landing-Auto-Relay
  - 🚄 Landing-Fast-Relay
  - 🇺🇸 Landing-US-Relay
  - 🏠 Landing-Home-Relay
  icon: https://fastly.jsdelivr.net/gh/yz0812/mypic@master/Clash_Verge_Rev/10007.svg
- interval: 600
  timeout: 5000
  url: http://www.gstatic.com/generate_204
  lazy: true
  max-failed-times: 3
  hidden: false
  disable-udp: false
  filter: 香港|HK|🇭🇰
  exclude-filter: (?i)(free|免费|2.5|1.7)
  tcp-keep-alive: true
  tcp-no-delay: true
  tcp-fast-open: true
  connection-reuse: true
  persistent-connection: false
  connection-idle-timeout: 300
  expected-status: 204
  tolerance: 50
  type: url-test
  strategy: sticky-sessions
  include-all: false
  use:
  - cordcloud
  - creativity-1
  - 宝可梦-1
  - 免流云
  name: 🇭🇰 香港
  icon: https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/Hong_Kong.png
- type: select
  disable-udp: false
  hidden: false
  include-all: false
  interval: 0
  timeout: 0
  lazy: true
  max-failed-times: 0
  name: ⚙️ 自动选择
  proxies:
  - 🎯 落地节点
  - 🇭🇰 香港
  - ⚡ 深港专线
  - 🚄 3.0倍率
  - 🇺🇸 美国
  - 🏠 家宽
  - 🌍 欧洲
  - 🇹🇼 台湾
  - 🇯🇵 日本
  - 🇰🇷 韩国
  - 🇸🇬 新加坡
  - CC CordCloud
  - CT1 CreaTivity-1
  - PKM1 宝可梦-1
  - MLY 免流云
  icon: https://fastly.jsdelivr.net/gh/yz0812/mypic@master/Clash_Verge_Rev/10002.svg
- type: select
  disable-udp: false
  hidden: true
  include-all: false
  interval: 0
  timeout: 0
  lazy: true
  max-failed-times: 0
  name: 🥰 广告拦截
  proxies:
  - REJECT
  - DIRECT
  icon: https://fastly.jsdelivr.net/gh/yz0812/mypic@master/Clash_Verge_Rev/10009.svg
- type: select
  disable-udp: false
  hidden: true
  include-all: false
  interval: 0
  timeout: 0
  lazy: true
  max-failed-times: 0
  name: 🔗 直连
  proxies:
  - DIRECT
  icon: https://fastly.jsdelivr.net/gh/yz0812/mypic@master/Clash_Verge_Rev/10041.svg
- interval: 0
  timeout: 10000
  url: http://www.gstatic.com/generate_204
  lazy: true
  max-failed-times: 0
  hidden: false
  disable-udp: false
  filter: ''
  exclude-filter: ''
  tcp-keep-alive: true
  tcp-no-delay: true
  tcp-fast-open: true
  connection-reuse: true
  persistent-connection: true
  connection-idle-timeout: 2400
  expected-status: 204
  tolerance: 50
  type: select
  include-all: false
  keep-alive-interval: 300
  session-sticky: true
  name: 💻 Copilot
  proxies:
  - 🎯 落地节点
  - 🔗 直连
  - 🇺🇸 美国-AI
  - 🏠 家宽-AI
  - 🌍 欧洲-AI
  - 🇭🇰 香港-AI
  - 🇹🇼 台湾-AI
  - 🇯🇵 日本-AI
  - 🇰🇷 韩国-AI
  - 🇸🇬 新加坡-AI
  - CC CordCloud
  - CT1 CreaTivity-1
  - PKM1 宝可梦-1
  - MLY 免流云
  icon: https://fastly.jsdelivr.net/gh/yz0812/mypic@master/Clash_Verge_Rev/github-copilot.svg
- interval: 0
  timeout: 10000
  url: http://www.gstatic.com/generate_204
  lazy: true
  max-failed-times: 0
  hidden: false
  disable-udp: false
  filter: ''
  exclude-filter: ''
  tcp-keep-alive: true
  tcp-no-delay: true
  tcp-fast-open: true
  connection-reuse: true
  persistent-connection: true
  connection-idle-timeout: 2400
  expected-status: 204
  tolerance: 50
  type: select
  include-all: false
  keep-alive-interval: 300
  session-sticky: true
  name: ✨ Gemini
  proxies:
  - 🎯 落地节点
  - 🇺🇸 美国-AI
  - 🏠 家宽-AI
  - 🌍 欧洲-AI
  - 🇭🇰 香港-AI
  - 🇹🇼 台湾-AI
  - 🇯🇵 日本-AI
  - 🇰🇷 韩国-AI
  - 🇸🇬 新加坡-AI
  - CC CordCloud
  - CT1 CreaTivity-1
  - PKM1 宝可梦-1
  - MLY 免流云
  icon: https://fastly.jsdelivr.net/gh/yz0812/mypic@master/Clash_Verge_Rev/gemini.png
- interval: 0
  timeout: 10000
  url: http://www.gstatic.com/generate_204
  lazy: true
  max-failed-times: 0
  hidden: false
  disable-udp: false
  filter: ''
  exclude-filter: ''
  tcp-keep-alive: true
  tcp-no-delay: true
  tcp-fast-open: true
  connection-reuse: true
  persistent-connection: true
  connection-idle-timeout: 2400
  expected-status: 204
  tolerance: 50
  type: select
  include-all: false
  keep-alive-interval: 300
  session-sticky: true
  name: 🤖 OpenAI
  proxies:
  - 🎯 落地节点
  - 🇺🇸 美国-AI
  - 🏠 家宽-AI
  - 🌍 欧洲-AI
  - 🇭🇰 香港-AI
  - 🇹🇼 台湾-AI
  - 🇯🇵 日本-AI
  - 🇰🇷 韩国-AI
  - 🇸🇬 新加坡-AI
  - CC CordCloud
  - CT1 CreaTivity-1
  - PKM1 宝可梦-1
  - MLY 免流云
  icon: https://fastly.jsdelivr.net/gh/yz0812/mypic@master/Clash_Verge_Rev/10028.svg
- interval: 0
  timeout: 10000
  url: http://www.gstatic.com/generate_204
  lazy: true
  max-failed-times: 0
  hidden: false
  disable-udp: false
  filter: ''
  exclude-filter: ''
  tcp-keep-alive: true
  tcp-no-delay: true
  tcp-fast-open: true
  connection-reuse: true
  persistent-connection: true
  connection-idle-timeout: 2400
  expected-status: 204
  tolerance: 50
  type: select
  include-all: false
  keep-alive-interval: 300
  session-sticky: true
  name: 🤖 Grok
  proxies:
  - 🎯 落地节点
  - 🇺🇸 美国-AI
  - 🏠 家宽-AI
  - 🌍 欧洲-AI
  - 🇭🇰 香港-AI
  - 🇹🇼 台湾-AI
  - 🇯🇵 日本-AI
  - 🇰🇷 韩国-AI
  - 🇸🇬 新加坡-AI
  - 🔗 直连
  - CC CordCloud
  - CT1 CreaTivity-1
  - PKM1 宝可梦-1
  - MLY 免流云
  icon: https://fastly.jsdelivr.net/gh/yz0812/mypic@master/Clash_Verge_Rev/grok.svg
- type: select
  disable-udp: false
  hidden: false
  include-all: false
  interval: 0
  timeout: 10000
  lazy: true
  max-failed-times: 0
  connection-idle-timeout: 2400
  persistent-connection: true
  tcp-keep-alive: true
  keep-alive-interval: 300
  session-sticky: true
  name: 🚀 Augment
  proxies:
  - 🇯🇵 日本-AI
  - 🇺🇸 美国-AI
  - 🏠 家宽-AI
  - 🌍 欧洲-AI
  - 🇭🇰 香港-AI
  - 🇹🇼 台湾-AI
  - 🇰🇷 韩国-AI
  - 🇸🇬 新加坡-AI
  - ⚡ 深港专线
  - 🔗 直连
  - CC CordCloud
  - CT1 CreaTivity-1
  - PKM1 宝可梦-1
  - MLY 免流云
  icon: https://fastly.jsdelivr.net/gh/yz0812/mypic@master/Clash_Verge_Rev/10028.svg
- type: select
  disable-udp: false
  hidden: false
  include-all: false
  interval: 0
  timeout: 8000
  lazy: true
  max-failed-times: 0
  name: 💻 Cursor
  proxies:
  - 🇸🇬 新加坡-AI
  - 🎯 落地节点
  - 🇺🇸 美国-AI
  - 🏠 家宽-AI
  - 🌍 欧洲-AI
  - 🇭🇰 香港-AI
  - 🇹🇼 台湾-AI
  - 🇯🇵 日本-AI
  - 🇰🇷 韩国-AI
  - CC CordCloud
  - CT1 CreaTivity-1
  - PKM1 宝可梦-1
  - MLY 免流云
  connection-idle-timeout: 3600
  persistent-connection: true
  tcp-keep-alive: true
  keep-alive-interval: 180
  session-sticky: true
  max-connection-age: 10800
  connection-reuse-limit: 1000
  icon: https://fastly.jsdelivr.net/gh/yz0812/mypic@master/Clash_Verge_Rev/cursor.svg
- interval: 0
  timeout: 10000
  url: http://www.gstatic.com/generate_204
  lazy: true
  max-failed-times: 0
  hidden: false
  disable-udp: false
  filter: ''
  exclude-filter: ''
  tcp-keep-alive: true
  tcp-no-delay: true
  tcp-fast-open: true
  connection-reuse: true
  persistent-connection: true
  connection-idle-timeout: 2400
  expected-status: 204
  tolerance: 50
  type: select
  include-all: false
  keep-alive-interval: 300
  session-sticky: true
  name: 🧠 Claude
  proxies:
  - 🎯 落地节点
  - 🇺🇸 美国-AI
  - 🏠 家宽-AI
  - 🌍 欧洲-AI
  - 🇭🇰 香港-AI
  - 🇹🇼 台湾-AI
  - 🇯🇵 日本-AI
  - 🇰🇷 韩国-AI
  - 🇸🇬 新加坡-AI
  - CC CordCloud
  - CT1 CreaTivity-1
  - PKM1 宝可梦-1
  - MLY 免流云
  icon: https://fastly.jsdelivr.net/gh/yz0812/mypic@master/Clash_Verge_Rev/claude.png
- type: select
  disable-udp: false
  hidden: false
  include-all: false
  interval: 0
  timeout: 0
  lazy: true
  max-failed-times: 0
  name: 📱 GitHub
  proxies:
  - 🎯 落地节点
  - ⚙️ 自动选择
  - ⚡ 深港专线
  - 🇺🇸 美国
  - 🏠 家宽
  - 🌍 欧洲
  - 🇭🇰 香港
  - 🇹🇼 台湾
  - 🇯🇵 日本
  - 🇰🇷 韩国
  - 🇸🇬 新加坡
  - 🔗 直连
  - CC CordCloud
  - CT1 CreaTivity-1
  - PKM1 宝可梦-1
  - MLY 免流云
  icon: https://fastly.jsdelivr.net/gh/yz0812/mypic@master/Clash_Verge_Rev/10001.svg
- type: select
  disable-udp: false
  hidden: false
  include-all: false
  interval: 0
  timeout: 5000
  lazy: true
  max-failed-times: 0
  name: 📢 Google
  proxies:
  - 🎯 落地节点
  - ⚙️ 自动选择
  - ⚡ 深港专线
  - 🇺🇸 美国
  - 🏠 家宽
  - 🌍 欧洲
  - 🇭🇰 香港
  - 🇹🇼 台湾
  - 🇯🇵 日本
  - 🇰🇷 韩国
  - 🇸🇬 新加坡
  - CC CordCloud
  - CT1 CreaTivity-1
  - PKM1 宝可梦-1
  - MLY 免流云
  icon: https://fastly.jsdelivr.net/gh/yz0812/mypic@master/Clash_Verge_Rev/10020.svg
- type: select
  disable-udp: false
  hidden: false
  include-all: false
  interval: 0
  timeout: 0
  lazy: true
  max-failed-times: 0
  name: 🎬 Netflix
  proxies:
  - 🎯 落地节点
  - ⚙️ 自动选择
  - 🇺🇸 美国
  - 🏠 家宽
  - 🌍 欧洲
  - 🇭🇰 香港
  - 🇹🇼 台湾
  - 🇯🇵 日本
  - 🇰🇷 韩国
  - 🇸🇬 新加坡
  - 🔗 直连
  - CC CordCloud
  - CT1 CreaTivity-1
  - PKM1 宝可梦-1
  - MLY 免流云
  icon: https://fastly.jsdelivr.net/gh/yz0812/mypic@master/Clash_Verge_Rev/10026.svg
- type: select
  disable-udp: false
  hidden: false
  include-all: false
  interval: 0
  timeout: 0
  lazy: true
  max-failed-times: 0
  name: 🎮 游戏服务
  proxies:
  - 🎯 落地节点
  - 🔗 直连
  - ⚙️ 自动选择
  - ⚡ 深港专线
  - 🇺🇸 美国
  - 🏠 家宽
  - 🌍 欧洲
  - 🇭🇰 香港
  - 🇹🇼 台湾
  - 🇯🇵 日本
  - 🇰🇷 韩国
  - 🇸🇬 新加坡
  - CC CordCloud
  - CT1 CreaTivity-1
  - PKM1 宝可梦-1
  - MLY 免流云
  icon: https://github.com/DustinWin/ruleset_geodata/releases/download/icons/games-cn.png
- type: select
  disable-udp: false
  hidden: false
  include-all: false
  interval: 0
  timeout: 0
  lazy: true
  max-failed-times: 0
  name: Ⓜ️ Microsoft
  proxies:
  - 🎯 落地节点
  - 🔗 直连
  - ⚙️ 自动选择
  - 🇺🇸 美国
  - 🏠 家宽
  - 🌍 欧洲
  - 🇭🇰 香港
  - 🇹🇼 台湾
  - 🇯🇵 日本
  - 🇰🇷 韩国
  - 🇸🇬 新加坡
  - CC CordCloud
  - CT1 CreaTivity-1
  - PKM1 宝可梦-1
  - MLY 免流云
  icon: https://fastly.jsdelivr.net/gh/yz0812/mypic@master/Clash_Verge_Rev/10014.svg
- type: select
  disable-udp: false
  hidden: false
  include-all: false
  interval: 0
  timeout: 0
  lazy: true
  max-failed-times: 0
  name: ☁️ OneDrive
  proxies:
  - 🎯 落地节点
  - 🔗 直连
  - ⚙️ 自动选择
  - 🇺🇸 美国
  - 🏠 家宽
  - 🌍 欧洲
  - 🇭🇰 香港
  - 🇹🇼 台湾
  - 🇯🇵 日本
  - 🇰🇷 韩国
  - 🇸🇬 新加坡
  - CC CordCloud
  - CT1 CreaTivity-1
  - PKM1 宝可梦-1
  - MLY 免流云
  icon: https://fastly.jsdelivr.net/gh/yz0812/mypic@master/Clash_Verge_Rev/10040.svg
- type: select
  disable-udp: false
  hidden: false
  include-all: false
  interval: 0
  timeout: 0
  lazy: true
  max-failed-times: 0
  name: 🍎 苹果服务
  proxies:
  - 🎯 落地节点
  - 🔗 直连
  - ⚙️ 自动选择
  - 🇺🇸 美国
  - 🏠 家宽
  - 🌍 欧洲
  - 🇭🇰 香港
  - 🇹🇼 台湾
  - 🇯🇵 日本
  - 🇰🇷 韩国
  - 🇸🇬 新加坡
  - CC CordCloud
  - CT1 CreaTivity-1
  - PKM1 宝可梦-1
  - MLY 免流云
  icon: https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/apple.svg
- type: select
  disable-udp: false
  hidden: false
  include-all: false
  interval: 0
  timeout: 0
  lazy: true
  max-failed-times: 0
  name: 📲 电报消息
  proxies:
  - 🎯 落地节点
  - ⚙️ 自动选择
  - ⚡ 深港专线
  - 🇺🇸 美国
  - 🏠 家宽
  - 🌍 欧洲
  - 🇭🇰 香港
  - 🇹🇼 台湾
  - 🇯🇵 日本
  - 🇰🇷 韩国
  - 🇸🇬 新加坡
  - 🔗 直连
  - CC CordCloud
  - CT1 CreaTivity-1
  - PKM1 宝可梦-1
  - MLY 免流云
  icon: https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/telegram.svg
- type: select
  disable-udp: false
  hidden: false
  include-all: false
  interval: 0
  timeout: 0
  lazy: true
  max-failed-times: 0
  strategy: sticky-sessions
  use:
  - cordcloud
  - creativity-1
  - 宝可梦-1
  - 免流云
  name: 🏠 家宽
  filter: (?i)(家宽|住宅|residential|home|broadband|宽带|民宅|家庭)
  icon: https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/Scholar.png
  exclude-filter: ((?i)(free|免费|ai|专用))|((?i)(landing|落地|webshare))
- interval: 600
  timeout: 5000
  url: http://www.gstatic.com/generate_204
  lazy: true
  max-failed-times: 3
  hidden: false
  disable-udp: false
  filter: (?i)(越|土|英|法|德|意|西|荷|瑞|奥|比|挪|瑞典|丹|芬|波|捷|匈|罗|保|希|葡|爱|冰|卢|伦敦|巴黎|柏林|罗马|马德里|阿姆斯特丹|苏黎世|维也纳|布鲁塞尔|奥斯陆|斯德哥尔摩|哥本哈根|赫尔辛基|华沙|布拉格|布达佩斯|布加勒斯特|索非亚|雅典|里斯本|都柏林|雷克雅未克)
  exclude-filter: (?i)(free|免费|ai|专用)
  tcp-keep-alive: true
  tcp-no-delay: true
  tcp-fast-open: true
  connection-reuse: true
  persistent-connection: false
  connection-idle-timeout: 300
  expected-status: 204
  tolerance: 100
  type: url-test
  strategy: sticky-sessions
  include-all: false
  use:
  - cordcloud
  - creativity-1
  - 宝可梦-1
  - 免流云
  name: 🌍 欧洲
  icon: https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/Europe_Map.png
- interval: 600
  timeout: 5000
  url: http://www.gstatic.com/generate_204
  lazy: true
  max-failed-times: 3
  hidden: false
  disable-udp: false
  filter: 台湾|TW|🇹🇼
  exclude-filter: (?i)(free|免费)
  tcp-keep-alive: true
  tcp-no-delay: true
  tcp-fast-open: true
  connection-reuse: true
  persistent-connection: false
  connection-idle-timeout: 300
  expected-status: 204
  tolerance: 50
  type: url-test
  strategy: sticky-sessions
  include-all: false
  use:
  - cordcloud
  - creativity-1
  - 宝可梦-1
  - 免流云
  name: 🇹🇼 台湾
  icon: https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/Taiwan.png
- interval: 600
  timeout: 5000
  url: http://www.gstatic.com/generate_204
  lazy: true
  max-failed-times: 3
  hidden: false
  disable-udp: false
  filter: 日本|JP|🇯🇵
  exclude-filter: (?i)(free|免费)
  tcp-keep-alive: true
  tcp-no-delay: true
  tcp-fast-open: true
  connection-reuse: true
  persistent-connection: false
  connection-idle-timeout: 300
  expected-status: 204
  tolerance: 50
  type: url-test
  strategy: sticky-sessions
  include-all: false
  use:
  - cordcloud
  - creativity-1
  - 宝可梦-1
  - 免流云
  name: 🇯🇵 日本
  icon: https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/Japan.png
- interval: 600
  timeout: 5000
  url: http://www.gstatic.com/generate_204
  lazy: true
  max-failed-times: 3
  hidden: false
  disable-udp: false
  filter: 韩国|KR|🇰🇷
  exclude-filter: (?i)(free|免费)
  tcp-keep-alive: true
  tcp-no-delay: true
  tcp-fast-open: true
  connection-reuse: true
  persistent-connection: false
  connection-idle-timeout: 300
  expected-status: 204
  tolerance: 50
  type: url-test
  strategy: sticky-sessions
  include-all: false
  use:
  - cordcloud
  - creativity-1
  - 宝可梦-1
  - 免流云
  name: 🇰🇷 韩国
  icon: https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/Korea.png
- interval: 600
  timeout: 5000
  url: http://www.gstatic.com/generate_204
  lazy: true
  max-failed-times: 3
  hidden: false
  disable-udp: false
  filter: 新加坡|SG|🇸🇬
  exclude-filter: (?i)(free|免费|ai|专用)
  tcp-keep-alive: true
  tcp-no-delay: true
  tcp-fast-open: true
  connection-reuse: true
  persistent-connection: false
  connection-idle-timeout: 300
  expected-status: 204
  tolerance: 100
  type: url-test
  strategy: sticky-sessions
  include-all: false
  use:
  - cordcloud
  - creativity-1
  - 宝可梦-1
  - 免流云
  name: 🇸🇬 新加坡
  icon: https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/Singapore.png
- type: select
  disable-udp: false
  hidden: false
  include-all: false
  interval: 0
  timeout: 0
  lazy: true
  max-failed-times: 0
  strategy: sticky-sessions
  use:
  - cordcloud
  - creativity-1
  - 宝可梦-1
  - 免流云
  name: 🏠 家宽-AI
  filter: (?i)(家宽|住宅|residential|home|broadband|宽带|民宅|家庭)
  icon: https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/Scholar.png
  exclude-filter: (?i)(free|免费)
- type: select
  disable-udp: false
  hidden: false
  include-all: false
  interval: 0
  timeout: 0
  lazy: true
  max-failed-times: 0
  strategy: sticky-sessions
  use:
  - cordcloud
  - creativity-1
  - 宝可梦-1
  - 免流云
  name: 🌍 欧洲-AI
  filter: (?i)(越|土|英|法|德|意|西|荷|瑞|奥|比|挪|瑞典|丹|芬|波|捷|匈|罗|保|希|葡|爱|冰|卢|伦敦|巴黎|柏林|罗马|马德里|阿姆斯特丹|苏黎世|维也纳|布鲁塞尔|奥斯陆|斯德哥尔摩|哥本哈根|赫尔辛基|华沙|布拉格|布达佩斯|布加勒斯特|索非亚|雅典|里斯本|都柏林|雷克雅未克)
  icon: https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/Europe_Map.png
  exclude-filter: (?i)(free|免费)
- type: select
  disable-udp: false
  hidden: false
  include-all: false
  interval: 0
  timeout: 0
  lazy: true
  max-failed-times: 0
  strategy: sticky-sessions
  use:
  - cordcloud
  - creativity-1
  - 宝可梦-1
  - 免流云
  name: 🇭🇰 香港-AI
  filter: 香港|HK|🇭🇰
  icon: https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/Hong_Kong.png
  exclude-filter: (?i)(free|免费)
- type: select
  disable-udp: false
  hidden: false
  include-all: false
  interval: 0
  timeout: 0
  lazy: true
  max-failed-times: 0
  strategy: sticky-sessions
  use:
  - cordcloud
  - creativity-1
  - 宝可梦-1
  - 免流云
  name: 🇹🇼 台湾-AI
  filter: 台湾|TW|🇹🇼
  icon: https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/Taiwan.png
  exclude-filter: (?i)(free|免费)
- type: select
  disable-udp: false
  hidden: false
  include-all: false
  interval: 0
  timeout: 0
  lazy: true
  max-failed-times: 0
  strategy: sticky-sessions
  use:
  - cordcloud
  - creativity-1
  - 宝可梦-1
  - 免流云
  name: 🇯🇵 日本-AI
  filter: 日本|JP|🇯🇵
  icon: https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/Japan.png
  exclude-filter: (?i)(free|免费)
- type: select
  disable-udp: false
  hidden: false
  include-all: false
  interval: 0
  timeout: 0
  lazy: true
  max-failed-times: 0
  strategy: sticky-sessions
  use:
  - cordcloud
  - creativity-1
  - 宝可梦-1
  - 免流云
  name: 🇰🇷 韩国-AI
  filter: 韩国|KR|🇰🇷
  icon: https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/Korea.png
  exclude-filter: (?i)(free|免费)
- type: select
  disable-udp: false
  hidden: false
  include-all: false
  interval: 0
  timeout: 0
  lazy: true
  max-failed-times: 0
  strategy: sticky-sessions
  use:
  - cordcloud
  - creativity-1
  - 宝可梦-1
  - 免流云
  name: 🇸🇬 新加坡-AI
  filter: 新加坡|SG|🇸🇬
  icon: https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/Singapore.png
  exclude-filter: (?i)(free|免费|游戏|game)
- type: select
  disable-udp: false
  hidden: false
  include-all: false
  interval: 0
  timeout: 0
  lazy: true
  max-failed-times: 0
  name: 🐟 漏网之鱼
  proxies:
  - 🎯 落地节点
  - ⚙️ 自动选择
  - ⚡ 深港专线
  - 🇺🇸 美国
  - 🏠 家宽
  - 🌍 欧洲
  - 🇭🇰 香港
  - 🇹🇼 台湾
  - 🇯🇵 日本
  - 🇰🇷 韩国
  - 🇸🇬 新加坡
  - 🔗 直连
  - CC CordCloud
  - CT1 CreaTivity-1
  - PKM1 宝可梦-1
  - MLY 免流云
  icon: https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/fish.svg
- interval: 600
  timeout: 5000
  url: http://www.gstatic.com/generate_204
  lazy: true
  max-failed-times: 3
  hidden: false
  disable-udp: false
  filter: ''
  exclude-filter: ''
  tcp-keep-alive: true
  tcp-no-delay: true
  tcp-fast-open: true
  connection-reuse: true
  persistent-connection: false
  connection-idle-timeout: 300
  expected-status: 204
  tolerance: 100
  type: url-test
  strategy: sticky-sessions
  include-all: false
  use:
  - cordcloud
  name: CC CordCloud
  icon: https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/Siri.png
- interval: 600
  timeout: 5000
  url: http://www.gstatic.com/generate_204
  lazy: true
  max-failed-times: 3
  hidden: false
  disable-udp: false
  filter: ''
  exclude-filter: ''
  tcp-keep-alive: true
  tcp-no-delay: true
  tcp-fast-open: true
  connection-reuse: true
  persistent-connection: false
  connection-idle-timeout: 300
  expected-status: 204
  tolerance: 100
  type: url-test
  strategy: sticky-sessions
  include-all: false
  use:
  - creativity-1
  name: CT1 CreaTivity-1
  icon: https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/Siri.png
- interval: 600
  timeout: 5000
  url: http://www.gstatic.com/generate_204
  lazy: true
  max-failed-times: 3
  hidden: false
  disable-udp: false
  filter: ''
  exclude-filter: ''
  tcp-keep-alive: true
  tcp-no-delay: true
  tcp-fast-open: true
  connection-reuse: true
  persistent-connection: false
  connection-idle-timeout: 300
  expected-status: 204
  tolerance: 50
  type: url-test
  strategy: sticky-sessions
  include-all: false
  use:
  - 宝可梦-1
  name: PKM1 宝可梦-1
  icon: https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/Siri.png
- interval: 600
  timeout: 5000
  url: http://www.gstatic.com/generate_204
  lazy: true
  max-failed-times: 3
  hidden: false
  disable-udp: false
  filter: ''
  exclude-filter: ''
  tcp-keep-alive: true
  tcp-no-delay: true
  tcp-fast-open: true
  connection-reuse: true
  persistent-connection: false
  connection-idle-timeout: 300
  expected-status: 204
  tolerance: 50
  type: url-test
  strategy: sticky-sessions
  include-all: false
  use:
  - 免流云
  name: MLY 免流云
  icon: https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/Siri.png
rule-providers:
  reject:
    type: http
    format: yaml
    interval: 86400
    timeout: 30000
    retry: 2
    behavior: domain
    url: https://fastly.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/reject.txt
    path: ./ruleset/loyalsoldier/reject.yaml
  icloud:
    type: http
    format: yaml
    interval: 86400
    timeout: 30000
    retry: 2
    behavior: domain
    url: https://fastly.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/icloud.txt
    path: ./ruleset/loyalsoldier/icloud.yaml
  apple:
    type: http
    format: yaml
    interval: 86400
    timeout: 30000
    retry: 2
    behavior: domain
    url: https://fastly.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/apple.txt
    path: ./ruleset/loyalsoldier/apple.yaml
  gemini:
    type: http
    format: yaml
    interval: 86400
    timeout: 30000
    retry: 2
    behavior: classical
    url: https://cdn.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Clash/Gemini/Gemini.yaml
    path: ./ruleset/blackmatrix7/gemini.yaml
  google:
    type: http
    format: yaml
    interval: 86400
    timeout: 30000
    retry: 2
    behavior: domain
    url: https://fastly.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/google.txt
    path: ./ruleset/loyalsoldier/google.yaml
  proxy:
    type: http
    format: yaml
    interval: 86400
    timeout: 30000
    retry: 2
    behavior: domain
    url: https://fastly.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/proxy.txt
    path: ./ruleset/loyalsoldier/proxy.yaml
  direct:
    type: http
    format: yaml
    interval: 86400
    timeout: 30000
    retry: 2
    behavior: domain
    url: https://fastly.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/direct.txt
    path: ./ruleset/loyalsoldier/direct.yaml
  private:
    type: http
    format: yaml
    interval: 86400
    timeout: 30000
    retry: 2
    behavior: domain
    url: https://fastly.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/private.txt
    path: ./ruleset/loyalsoldier/private.yaml
  gfw:
    type: http
    format: yaml
    interval: 86400
    timeout: 30000
    retry: 2
    behavior: domain
    url: https://fastly.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/gfw.txt
    path: ./ruleset/loyalsoldier/gfw.yaml
  tld-not-cn:
    type: http
    format: yaml
    interval: 86400
    timeout: 30000
    retry: 2
    behavior: domain
    url: https://fastly.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/tld-not-cn.txt
    path: ./ruleset/loyalsoldier/tld-not-cn.yaml
  telegramcidr:
    type: http
    format: yaml
    interval: 86400
    timeout: 30000
    retry: 2
    behavior: ipcidr
    url: https://fastly.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/telegramcidr.txt
    path: ./ruleset/loyalsoldier/telegramcidr.yaml
  cncidr:
    type: http
    format: yaml
    interval: 86400
    timeout: 30000
    retry: 2
    behavior: ipcidr
    url: https://fastly.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/cncidr.txt
    path: ./ruleset/loyalsoldier/cncidr.yaml
  lancidr:
    type: http
    format: yaml
    interval: 86400
    timeout: 30000
    retry: 2
    behavior: ipcidr
    url: https://fastly.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/lancidr.txt
    path: ./ruleset/loyalsoldier/lancidr.yaml
  applications:
    type: http
    format: yaml
    interval: 86400
    timeout: 30000
    retry: 2
    behavior: classical
    url: https://fastly.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/applications.txt
    path: ./ruleset/loyalsoldier/applications.yaml
  advertising:
    type: http
    format: yaml
    interval: 86400
    timeout: 30000
    retry: 2
    behavior: classical
    url: https://cdn.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Clash/AdvertisingLite/AdvertisingLite_Classical.yaml
    path: ./ruleset/blackmatrix7/advertising.yaml
  copilot:
    type: http
    format: yaml
    interval: 86400
    timeout: 30000
    retry: 2
    behavior: classical
    url: https://cdn.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Clash/Copilot/Copilot.yaml
    path: ./ruleset/blackmatrix7/copilot.yaml
  github:
    type: http
    format: yaml
    interval: 86400
    timeout: 30000
    retry: 2
    behavior: classical
    url: https://cdn.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Clash/GitHub/GitHub.yaml
    path: ./ruleset/blackmatrix7/github.yaml
  google-rules:
    type: http
    format: yaml
    interval: 86400
    timeout: 30000
    retry: 2
    behavior: classical
    url: https://cdn.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Clash/Google/Google.yaml
    path: ./ruleset/blackmatrix7/google.yaml
  openai:
    type: http
    format: yaml
    interval: 86400
    timeout: 30000
    retry: 2
    behavior: classical
    url: https://cdn.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Clash/OpenAI/OpenAI.yaml
    path: ./ruleset/blackmatrix7/openai.yaml
  netflix:
    type: http
    format: yaml
    interval: 86400
    timeout: 30000
    retry: 2
    behavior: classical
    url: https://cdn.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Clash/Netflix/Netflix.yaml
    path: ./ruleset/blackmatrix7/netflix.yaml
  microsoft:
    type: http
    format: yaml
    interval: 86400
    timeout: 30000
    retry: 2
    behavior: classical
    url: https://cdn.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Clash/Microsoft/Microsoft.yaml
    path: ./ruleset/blackmatrix7/microsoft.yaml
  onedrive:
    type: http
    format: yaml
    interval: 86400
    timeout: 30000
    retry: 2
    behavior: classical
    url: https://cdn.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Clash/OneDrive/OneDrive.yaml
    path: ./ruleset/blackmatrix7/onedrive.yaml
  claude:
    type: http
    format: yaml
    interval: 86400
    timeout: 30000
    retry: 2
    behavior: classical
    url: https://cdn.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Clash/Claude/Claude.yaml
    path: ./ruleset/blackmatrix7/claude.yaml
  games-cn:
    type: http
    format: mrs
    interval: 86400
    timeout: 30000
    retry: 2
    behavior: domain
    url: https://github.com/DustinWin/ruleset_geodata/releases/download/mihomo-ruleset/games-cn.mrs
    path: ./ruleset/dustinwin/games-cn.mrs
rules:
- DST-PORT,22,DIRECT
- IP-CIDR,************/32,DIRECT
- DOMAIN-SUFFIX,cursor.sh,💻 Cursor
- DOMAIN-SUFFIX,cursor.com,💻 Cursor
- DOMAIN-SUFFIX,api.cursor.sh,💻 Cursor
- DOMAIN-SUFFIX,api.cursor.com,💻 Cursor
- DOMAIN-SUFFIX,auth.cursor.sh,💻 Cursor
- DOMAIN-SUFFIX,auth.cursor.com,💻 Cursor
- DOMAIN-SUFFIX,marketplace.cursorapi.com,💻 Cursor
- DOMAIN-SUFFIX,cursor-cdn.com,💻 Cursor
- DOMAIN-SUFFIX,download.todesktop.com,💻 Cursor
- DOMAIN-SUFFIX,elevenlabs.io,💻 Cursor
- DOMAIN-SUFFIX,api.elevenlabs.io,💻 Cursor
- DOMAIN-SUFFIX,cdn.cursor.sh,💻 Cursor
- DOMAIN-SUFFIX,assets.cursor.sh,💻 Cursor
- DOMAIN-KEYWORD,cursor,💻 Cursor
- DOMAIN-SUFFIX,cloudflare.com,🔗 直连
- DOMAIN-SUFFIX,yuh.cool,🔗 直连
- DOMAIN-KEYWORD,yuh.cool,🔗 直连
- RULE-SET,advertising,🥰 广告拦截
- DOMAIN-KEYWORD,analytics,🥰 广告拦截
- DOMAIN-KEYWORD,tracking,🥰 广告拦截
- DOMAIN-SUFFIX,googlesyndication.com,🥰 广告拦截
- DOMAIN-SUFFIX,googleadservices.com,🥰 广告拦截
- RULE-SET,applications,🔗 直连
- RULE-SET,private,🔗 直连
- DOMAIN-SUFFIX,demo.fuclaude.com,🔗 直连
- DOMAIN-SUFFIX,qq.com,🔗 直连
- DOMAIN-SUFFIX,aistudio.google.com,🎯 落地节点
- DOMAIN-SUFFIX,poolhub.me,🔗 直连
- DOMAIN-KEYWORD,qq,🔗 直连
- DOMAIN-KEYWORD,aizex,🔗 直连
- DOMAIN-KEYWORD,sacinfo,🔗 直连
- DOMAIN-KEYWORD,loongson,🔗 直连
- DOMAIN-KEYWORD,easychuan,🔗 直连
- DOMAIN-KEYWORD,deepseek,🔗 直连
- DOMAIN-KEYWORD,u-tools,🔗 直连
- DOMAIN-KEYWORD,webcatalog,🔗 直连
- DOMAIN-KEYWORD,linux.do,🔗 直连
- DOMAIN-SUFFIX,cdn.ldstatic.com,⚙️ 自动选择
- RULE-SET,gemini,✨ Gemini
- DOMAIN-KEYWORD,perplex,✨ Gemini
- RULE-SET,copilot,💻 Copilot
- DOMAIN-KEYWORD,copilot,💻 Copilot
- RULE-SET,github,📱 GitHub
- RULE-SET,google-rules,📢 Google
- RULE-SET,google,📢 Google
- RULE-SET,openai,🤖 OpenAI
- DOMAIN-KEYWORD,openai,🤖 OpenAI
- DOMAIN-KEYWORD,chatgpt,🤖 OpenAI
- DOMAIN-KEYWORD,augment,🚀 Augment
- RULE-SET,netflix,🎬 Netflix
- DOMAIN-SUFFIX,x.ai,🤖 Grok
- DOMAIN-SUFFIX,grok.x.ai,🤖 Grok
- DOMAIN-SUFFIX,grok-api.x.ai,🤖 Grok
- DOMAIN-KEYWORD,grok,🤖 Grok
- RULE-SET,games-cn,🎮 游戏服务
- DOMAIN-KEYWORD,youtube,🎬 Netflix
- DOMAIN-SUFFIX,googlevideo.com,🎬 Netflix
- RULE-SET,microsoft,Ⓜ️ Microsoft
- RULE-SET,onedrive,☁️ OneDrive
- RULE-SET,claude,🧠 Claude
- DOMAIN-KEYWORD,claude,🧠 Claude
- RULE-SET,icloud,🍎 苹果服务
- RULE-SET,apple,🍎 苹果服务
- RULE-SET,proxy,🔰 模式选择
- RULE-SET,gfw,🔰 模式选择
- RULE-SET,tld-not-cn,🔰 模式选择
- RULE-SET,direct,🔗 直连
- RULE-SET,lancidr,🔗 直连,no-resolve
- RULE-SET,cncidr,🔗 直连,no-resolve
- RULE-SET,telegramcidr,📲 电报消息,no-resolve
- GEOIP,LAN,🔗 直连,no-resolve
- GEOIP,CN,🔗 直连,no-resolve
- MATCH,🐟 漏网之鱼
